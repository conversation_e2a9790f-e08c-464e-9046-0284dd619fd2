import { Test, TestingModule } from '@nestjs/testing';
import { ContractService } from '../services/contract.service';
import { ContractRepository } from '../repositories/contract.repository';
import { Contract } from '../entities/contract.entity';
import { CreateContractDto } from '../dto/create-contract.dto';
import { ContractStatus } from '../enum/contract-status.enum';
import { ContractType } from '../enum/contract-type.enum';
import { AppException } from '@/common/exceptions/app.exception';
import { HRM_ERROR_CODES } from '../../errors/hrm-error.code';

/**
 * Comprehensive test suite for Contract module tenant isolation
 * Tests verify that all operations properly handle tenantId filtering
 */
describe('Contract Tenant Isolation', () => {
  let service: ContractService;
  let repository: ContractRepository;

  const mockContractRepository = {
    findAll: jest.fn(),
    findById: jest.fn(),
    findByContractCode: jest.fn(),
    findByEmployeeId: jest.fn(),
    findActiveContractByEmployeeId: jest.fn(),
    findContractsExpiringSoon: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    updateStatus: jest.fn(),
    terminateContract: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ContractService,
        {
          provide: ContractRepository,
          useValue: mockContractRepository,
        },
      ],
    }).compile();

    service = module.get<ContractService>(ContractService);
    repository = module.get<ContractRepository>(ContractRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Tenant Isolation - Repository Layer', () => {
    const tenant1Id = 1;
    const tenant2Id = 2;
    const contractId = 1;

    it('should pass tenantId to repository.findAll', async () => {
      const mockQuery = { page: 1, limit: 10 };
      const mockResult = {
        items: [{ id: contractId, tenantId: tenant1Id }] as Contract[],
        meta: { totalItems: 1, itemCount: 1, itemsPerPage: 10, totalPages: 1, currentPage: 1 },
      };

      mockContractRepository.findAll.mockResolvedValue(mockResult);

      await service.findAll(tenant1Id, mockQuery);

      expect(mockContractRepository.findAll).toHaveBeenCalledWith(tenant1Id, mockQuery);
      expect(mockContractRepository.findAll).toHaveBeenCalledTimes(1);
    });

    it('should pass tenantId to repository.findById', async () => {
      const mockContract = { id: contractId, tenantId: tenant1Id } as Contract;
      mockContractRepository.findById.mockResolvedValue(mockContract);

      await service.findById(tenant1Id, contractId);

      expect(mockContractRepository.findById).toHaveBeenCalledWith(tenant1Id, contractId);
      expect(mockContractRepository.findById).toHaveBeenCalledTimes(1);
    });

    it('should pass tenantId to repository.findByEmployeeId', async () => {
      const employeeId = 1;
      const mockContracts = [{ id: contractId, tenantId: tenant1Id, employeeId }] as Contract[];
      mockContractRepository.findByEmployeeId.mockResolvedValue(mockContracts);

      await service.findByEmployeeId(tenant1Id, employeeId);

      expect(mockContractRepository.findByEmployeeId).toHaveBeenCalledWith(tenant1Id, employeeId);
      expect(mockContractRepository.findByEmployeeId).toHaveBeenCalledTimes(1);
    });

    it('should pass tenantId to repository.findActiveContractByEmployeeId', async () => {
      const employeeId = 1;
      const mockContract = { id: contractId, tenantId: tenant1Id, employeeId } as Contract;
      mockContractRepository.findActiveContractByEmployeeId.mockResolvedValue(mockContract);

      await service.findActiveContractByEmployeeId(tenant1Id, employeeId);

      expect(mockContractRepository.findActiveContractByEmployeeId).toHaveBeenCalledWith(tenant1Id, employeeId);
      expect(mockContractRepository.findActiveContractByEmployeeId).toHaveBeenCalledTimes(1);
    });

    it('should pass tenantId to repository.findContractsExpiringSoon', async () => {
      const daysThreshold = 30;
      const mockContracts = [{ id: contractId, tenantId: tenant1Id }] as Contract[];
      mockContractRepository.findContractsExpiringSoon.mockResolvedValue(mockContracts);

      await service.findContractsExpiringSoon(tenant1Id, daysThreshold);

      expect(mockContractRepository.findContractsExpiringSoon).toHaveBeenCalledWith(tenant1Id, daysThreshold);
      expect(mockContractRepository.findContractsExpiringSoon).toHaveBeenCalledTimes(1);
    });

    it('should pass tenantId to repository.create', async () => {
      const userId = 1;
      const mockCreateDto: CreateContractDto = {
        contractCode: 'CT-2023-001',
        employeeId: 1,
        contractType: ContractType.DEFINITE,
        title: 'Employment Contract',
        startDate: new Date('2023-01-15'),
        baseSalary: 10000000,
      };
      const mockCreatedContract = { id: contractId, tenantId: tenant1Id, ...mockCreateDto } as Contract;

      mockContractRepository.findByContractCode.mockResolvedValue(null);
      mockContractRepository.create.mockResolvedValue(mockCreatedContract);

      await service.create(tenant1Id, mockCreateDto, userId);

      expect(mockContractRepository.findByContractCode).toHaveBeenCalledWith(tenant1Id, mockCreateDto.contractCode);
      expect(mockContractRepository.create).toHaveBeenCalledWith(tenant1Id, expect.any(Object));
    });

    it('should pass tenantId to repository.update', async () => {
      const userId = 1;
      const mockContract = { id: contractId, tenantId: tenant1Id, status: ContractStatus.DRAFT } as Contract;
      const mockUpdateDto = { title: 'Updated Contract' };

      mockContractRepository.findById.mockResolvedValue(mockContract);
      mockContractRepository.update.mockResolvedValue({ ...mockContract, ...mockUpdateDto } as Contract);

      await service.update(tenant1Id, contractId, mockUpdateDto, userId);

      expect(mockContractRepository.findById).toHaveBeenCalledWith(tenant1Id, contractId);
      expect(mockContractRepository.update).toHaveBeenCalledWith(tenant1Id, contractId, expect.any(Object));
    });

    it('should pass tenantId to repository.delete', async () => {
      const mockContract = { id: contractId, tenantId: tenant1Id } as Contract;

      mockContractRepository.findById.mockResolvedValue(mockContract);
      mockContractRepository.delete.mockResolvedValue(true);

      await service.delete(tenant1Id, contractId);

      expect(mockContractRepository.findById).toHaveBeenCalledWith(tenant1Id, contractId);
      expect(mockContractRepository.delete).toHaveBeenCalledWith(tenant1Id, contractId);
    });
  });

  describe('Cross-Tenant Access Prevention', () => {
    const tenant1Id = 1;
    const tenant2Id = 2;
    const contractId = 1;

    it('should not find contract from different tenant', async () => {
      // Contract belongs to tenant2, but we're searching with tenant1
      mockContractRepository.findById.mockResolvedValue(null);

      await expect(service.findById(tenant1Id, contractId)).rejects.toThrow(
        new AppException(HRM_ERROR_CODES.CONTRACT_NOT_FOUND, `Contract with ID ${contractId} not found`)
      );

      expect(mockContractRepository.findById).toHaveBeenCalledWith(tenant1Id, contractId);
    });

    it('should not create contract with duplicate code in different tenant', async () => {
      const userId = 1;
      const mockCreateDto: CreateContractDto = {
        contractCode: 'CT-2023-001',
        employeeId: 1,
        contractType: ContractType.DEFINITE,
        title: 'Employment Contract',
        startDate: new Date('2023-01-15'),
        baseSalary: 10000000,
      };

      // Contract code exists in tenant1, but we're creating in tenant2
      mockContractRepository.findByContractCode.mockResolvedValue(null); // No conflict in tenant2

      const mockCreatedContract = { id: contractId, tenantId: tenant2Id, ...mockCreateDto } as Contract;
      mockContractRepository.create.mockResolvedValue(mockCreatedContract);

      const result = await service.create(tenant2Id, mockCreateDto, userId);

      expect(result.tenantId).toBe(tenant2Id);
      expect(mockContractRepository.findByContractCode).toHaveBeenCalledWith(tenant2Id, mockCreateDto.contractCode);
      expect(mockContractRepository.create).toHaveBeenCalledWith(tenant2Id, expect.any(Object));
    });
  });

  describe('Tenant Data Consistency', () => {
    const tenantId = 1;
    const contractId = 1;

    it('should ensure all returned contracts belong to the correct tenant', async () => {
      const mockQuery = { page: 1, limit: 10 };
      const mockResult = {
        items: [
          { id: 1, tenantId, contractCode: 'CT-2023-001' },
          { id: 2, tenantId, contractCode: 'CT-2023-002' },
        ] as Contract[],
        meta: { totalItems: 2, itemCount: 2, itemsPerPage: 10, totalPages: 1, currentPage: 1 },
      };

      mockContractRepository.findAll.mockResolvedValue(mockResult);

      const result = await service.findAll(tenantId, mockQuery);

      // Verify all returned contracts have the correct tenantId
      result.items.forEach(contract => {
        expect(contract.tenantId).toBe(tenantId);
      });
    });

    it('should ensure created contract has correct tenantId', async () => {
      const userId = 1;
      const mockCreateDto: CreateContractDto = {
        contractCode: 'CT-2023-001',
        employeeId: 1,
        contractType: ContractType.DEFINITE,
        title: 'Employment Contract',
        startDate: new Date('2023-01-15'),
        baseSalary: 10000000,
      };

      const mockCreatedContract = { id: contractId, tenantId, ...mockCreateDto } as Contract;

      mockContractRepository.findByContractCode.mockResolvedValue(null);
      mockContractRepository.create.mockResolvedValue(mockCreatedContract);

      const result = await service.create(tenantId, mockCreateDto, userId);

      expect(result.tenantId).toBe(tenantId);
      expect(mockContractRepository.create).toHaveBeenCalledWith(
        tenantId,
        expect.objectContaining({
          contractCode: mockCreateDto.contractCode,
          employeeId: mockCreateDto.employeeId,
        })
      );
    });
  });
});
