import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { ContractsModule } from '../contracts.module';
import { Contract } from '../entities/contract.entity';
import { ContractType } from '../enum/contract-type.enum';
import { ContractStatus } from '../enum/contract-status.enum';
import { JwtUtilService, TokenType } from '@/modules/auth/guards/jwt.util';
import { AuthModule } from '@/modules/auth/auth.module';

/**
 * End-to-End tests for Contract API tenant isolation
 * Tests verify complete tenant isolation from API to database
 */
describe('Contract E2E Tenant Isolation', () => {
  let app: INestApplication;
  let jwtUtilService: JwtUtilService;

  // Test users from different tenants
  const tenant1User = {
    id: 1,
    email: '<EMAIL>',
    tenantId: 1,
    roles: ['user'],
  };

  const tenant2User = {
    id: 2,
    email: '<EMAIL>',
    tenantId: 2,
    roles: ['user'],
  };

  let tenant1Token: string;
  let tenant2Token: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        TypeOrmModule.forRootAsync({
          imports: [ConfigModule],
          useFactory: (configService: ConfigService) => ({
            type: 'postgres',
            host: configService.get('DB_HOST'),
            port: configService.get('DB_PORT'),
            username: configService.get('DB_USERNAME'),
            password: configService.get('DB_PASSWORD'),
            database: configService.get('DB_DATABASE'),
            entities: [Contract],
            synchronize: false,
            logging: false,
          }),
          inject: [ConfigService],
        }),
        JwtModule.registerAsync({
          imports: [ConfigModule],
          useFactory: (configService: ConfigService) => ({
            secret: configService.get('JWT_SECRET'),
            signOptions: { expiresIn: '1h' },
          }),
          inject: [ConfigService],
        }),
        AuthModule,
        ContractsModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    jwtUtilService = moduleFixture.get<JwtUtilService>(JwtUtilService);

    // Generate JWT tokens for test users
    tenant1Token = jwtUtilService.generateToken(tenant1User, TokenType.ACCESS);
    tenant2Token = jwtUtilService.generateToken(tenant2User, TokenType.ACCESS);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('GET /api/hrm/contracts - Tenant Isolation', () => {
    it('should return only contracts for tenant 1 when authenticated as tenant 1 user', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/hrm/contracts')
        .set('Authorization', `Bearer ${tenant1Token}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.items).toBeDefined();

      // All returned contracts should belong to tenant 1
      if (response.body.data.items.length > 0) {
        response.body.data.items.forEach((contract: any) => {
          expect(contract.tenantId).toBe(tenant1User.tenantId);
        });
      }
    });

    it('should return only contracts for tenant 2 when authenticated as tenant 2 user', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/hrm/contracts')
        .set('Authorization', `Bearer ${tenant2Token}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.items).toBeDefined();

      // All returned contracts should belong to tenant 2
      if (response.body.data.items.length > 0) {
        response.body.data.items.forEach((contract: any) => {
          expect(contract.tenantId).toBe(tenant2User.tenantId);
        });
      }
    });

    it('should return 401 when no authentication token provided', async () => {
      await request(app.getHttpServer())
        .get('/api/hrm/contracts')
        .expect(401);
    });
  });

  describe('POST /api/hrm/contracts - Tenant Isolation', () => {
    const contractData = {
      contractCode: 'CT-E2E-001',
      employeeId: 1,
      contractType: ContractType.DEFINITE,
      title: 'E2E Test Contract',
      startDate: '2023-01-15',
      baseSalary: 10000000,
    };

    it('should create contract with tenant 1 ID when authenticated as tenant 1 user', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/hrm/contracts')
        .set('Authorization', `Bearer ${tenant1Token}`)
        .send({ ...contractData, contractCode: 'CT-E2E-T1-001' })
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.tenantId).toBe(tenant1User.tenantId);
      expect(response.body.data.contractCode).toBe('CT-E2E-T1-001');
    });

    it('should create contract with tenant 2 ID when authenticated as tenant 2 user', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/hrm/contracts')
        .set('Authorization', `Bearer ${tenant2Token}`)
        .send({ ...contractData, contractCode: 'CT-E2E-T2-001' })
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.tenantId).toBe(tenant2User.tenantId);
      expect(response.body.data.contractCode).toBe('CT-E2E-T2-001');
    });

    it('should allow same contract code in different tenants', async () => {
      const sameContractCode = 'CT-E2E-SAME-001';

      // Create contract in tenant 1
      const response1 = await request(app.getHttpServer())
        .post('/api/hrm/contracts')
        .set('Authorization', `Bearer ${tenant1Token}`)
        .send({ ...contractData, contractCode: sameContractCode })
        .expect(201);

      expect(response1.body.data.tenantId).toBe(tenant1User.tenantId);

      // Create contract with same code in tenant 2 - should succeed
      const response2 = await request(app.getHttpServer())
        .post('/api/hrm/contracts')
        .set('Authorization', `Bearer ${tenant2Token}`)
        .send({ ...contractData, contractCode: sameContractCode })
        .expect(201);

      expect(response2.body.data.tenantId).toBe(tenant2User.tenantId);
    });
  });

  describe('GET /api/hrm/contracts/:id - Cross-Tenant Access Prevention', () => {
    let tenant1ContractId: number;
    let tenant2ContractId: number;

    beforeAll(async () => {
      // Create a contract in tenant 1
      const response1 = await request(app.getHttpServer())
        .post('/api/hrm/contracts')
        .set('Authorization', `Bearer ${tenant1Token}`)
        .send({
          contractCode: 'CT-CROSS-T1-001',
          employeeId: 1,
          contractType: ContractType.DEFINITE,
          title: 'Tenant 1 Contract',
          startDate: '2023-01-15',
          baseSalary: 10000000,
        });
      tenant1ContractId = response1.body.data.id;

      // Create a contract in tenant 2
      const response2 = await request(app.getHttpServer())
        .post('/api/hrm/contracts')
        .set('Authorization', `Bearer ${tenant2Token}`)
        .send({
          contractCode: 'CT-CROSS-T2-001',
          employeeId: 1,
          contractType: ContractType.DEFINITE,
          title: 'Tenant 2 Contract',
          startDate: '2023-01-15',
          baseSalary: 10000000,
        });
      tenant2ContractId = response2.body.data.id;
    });

    it('should allow tenant 1 user to access their own contract', async () => {
      const response = await request(app.getHttpServer())
        .get(`/api/hrm/contracts/${tenant1ContractId}`)
        .set('Authorization', `Bearer ${tenant1Token}`)
        .expect(200);

      expect(response.body.data.id).toBe(tenant1ContractId);
      expect(response.body.data.tenantId).toBe(tenant1User.tenantId);
    });

    it('should prevent tenant 1 user from accessing tenant 2 contract', async () => {
      await request(app.getHttpServer())
        .get(`/api/hrm/contracts/${tenant2ContractId}`)
        .set('Authorization', `Bearer ${tenant1Token}`)
        .expect(404); // Contract not found for this tenant
    });

    it('should prevent tenant 2 user from accessing tenant 1 contract', async () => {
      await request(app.getHttpServer())
        .get(`/api/hrm/contracts/${tenant1ContractId}`)
        .set('Authorization', `Bearer ${tenant2Token}`)
        .expect(404); // Contract not found for this tenant
    });

    it('should allow tenant 2 user to access their own contract', async () => {
      const response = await request(app.getHttpServer())
        .get(`/api/hrm/contracts/${tenant2ContractId}`)
        .set('Authorization', `Bearer ${tenant2Token}`)
        .expect(200);

      expect(response.body.data.id).toBe(tenant2ContractId);
      expect(response.body.data.tenantId).toBe(tenant2User.tenantId);
    });
  });

  describe('PATCH /api/hrm/contracts/:id - Cross-Tenant Update Prevention', () => {
    let tenant1ContractId: number;
    let tenant2ContractId: number;

    beforeAll(async () => {
      // Create contracts for cross-tenant update tests
      const response1 = await request(app.getHttpServer())
        .post('/api/hrm/contracts')
        .set('Authorization', `Bearer ${tenant1Token}`)
        .send({
          contractCode: 'CT-UPDATE-T1-001',
          employeeId: 1,
          contractType: ContractType.DEFINITE,
          title: 'Update Test Tenant 1',
          startDate: '2023-01-15',
          baseSalary: 10000000,
        });
      tenant1ContractId = response1.body.data.id;

      const response2 = await request(app.getHttpServer())
        .post('/api/hrm/contracts')
        .set('Authorization', `Bearer ${tenant2Token}`)
        .send({
          contractCode: 'CT-UPDATE-T2-001',
          employeeId: 1,
          contractType: ContractType.DEFINITE,
          title: 'Update Test Tenant 2',
          startDate: '2023-01-15',
          baseSalary: 10000000,
        });
      tenant2ContractId = response2.body.data.id;
    });

    it('should allow tenant 1 user to update their own contract', async () => {
      const updateData = { title: 'Updated by Tenant 1' };

      const response = await request(app.getHttpServer())
        .patch(`/api/hrm/contracts/${tenant1ContractId}`)
        .set('Authorization', `Bearer ${tenant1Token}`)
        .send(updateData)
        .expect(200);

      expect(response.body.data.title).toBe(updateData.title);
      expect(response.body.data.tenantId).toBe(tenant1User.tenantId);
    });

    it('should prevent tenant 1 user from updating tenant 2 contract', async () => {
      const updateData = { title: 'Malicious Update Attempt' };

      await request(app.getHttpServer())
        .patch(`/api/hrm/contracts/${tenant2ContractId}`)
        .set('Authorization', `Bearer ${tenant1Token}`)
        .send(updateData)
        .expect(404); // Contract not found for this tenant
    });
  });

  describe('DELETE /api/hrm/contracts/:id - Cross-Tenant Delete Prevention', () => {
    let tenant1ContractId: number;
    let tenant2ContractId: number;

    beforeAll(async () => {
      // Create contracts for cross-tenant delete tests
      const response1 = await request(app.getHttpServer())
        .post('/api/hrm/contracts')
        .set('Authorization', `Bearer ${tenant1Token}`)
        .send({
          contractCode: 'CT-DELETE-T1-001',
          employeeId: 1,
          contractType: ContractType.DEFINITE,
          title: 'Delete Test Tenant 1',
          startDate: '2023-01-15',
          baseSalary: 10000000,
        });
      tenant1ContractId = response1.body.data.id;

      const response2 = await request(app.getHttpServer())
        .post('/api/hrm/contracts')
        .set('Authorization', `Bearer ${tenant2Token}`)
        .send({
          contractCode: 'CT-DELETE-T2-001',
          employeeId: 1,
          contractType: ContractType.DEFINITE,
          title: 'Delete Test Tenant 2',
          startDate: '2023-01-15',
          baseSalary: 10000000,
        });
      tenant2ContractId = response2.body.data.id;
    });

    it('should prevent tenant 1 user from deleting tenant 2 contract', async () => {
      await request(app.getHttpServer())
        .delete(`/api/hrm/contracts/${tenant2ContractId}`)
        .set('Authorization', `Bearer ${tenant1Token}`)
        .expect(404); // Contract not found for this tenant
    });

    it('should allow tenant 1 user to delete their own contract', async () => {
      await request(app.getHttpServer())
        .delete(`/api/hrm/contracts/${tenant1ContractId}`)
        .set('Authorization', `Bearer ${tenant1Token}`)
        .expect(200);
    });
  });
});
