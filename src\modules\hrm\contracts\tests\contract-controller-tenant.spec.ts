import { Test, TestingModule } from '@nestjs/testing';
import { ContractController } from '../controllers/contract.controller';
import { ContractService } from '../services/contract.service';
import { Contract } from '../entities/contract.entity';
import { CreateContractDto } from '../dto/create-contract.dto';
import { UpdateContractDto } from '../dto/update-contract.dto';
import { ContractStatus } from '../enum/contract-status.enum';
import { ContractType } from '../enum/contract-type.enum';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';

/**
 * Integration tests for ContractController tenant isolation
 * Tests verify that @CurrentUser() decorator properly extracts tenantId
 * and passes it to service methods
 */
describe('ContractController Tenant Isolation', () => {
  let controller: ContractController;
  let service: ContractService;

  const mockContractService = {
    findAll: jest.fn(),
    findById: jest.fn(),
    findByEmployeeId: jest.fn(),
    findActiveContractByEmployeeId: jest.fn(),
    findContractsExpiringSoon: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    updateStatus: jest.fn(),
    terminateContract: jest.fn(),
    delete: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ContractController],
      providers: [
        {
          provide: ContractService,
          useValue: mockContractService,
        },
      ],
    }).compile();

    controller = module.get<ContractController>(ContractController);
    service = module.get<ContractService>(ContractService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Tenant ID Extraction from JWT', () => {
    const mockUser: JwtPayload = {
      id: 1,
      email: '<EMAIL>',
      tenantId: 1,
      roles: ['user'],
      iat: Date.now(),
      exp: Date.now() + 3600,
    };

    it('should extract tenantId from JWT and pass to service.findAll', async () => {
      const mockQuery = { page: 1, limit: 10 };
      const mockResult = {
        items: [{ id: 1, tenantId: mockUser.tenantId }] as Contract[],
        meta: { totalItems: 1, itemCount: 1, itemsPerPage: 10, totalPages: 1, currentPage: 1 },
      };

      mockContractService.findAll.mockResolvedValue(mockResult);

      await controller.findAll(mockQuery, mockUser);

      expect(mockContractService.findAll).toHaveBeenCalledWith(Number(mockUser.tenantId), mockQuery);
      expect(mockContractService.findAll).toHaveBeenCalledTimes(1);
    });

    it('should extract tenantId from JWT and pass to service.findById', async () => {
      const contractId = 1;
      const mockContract = { id: contractId, tenantId: mockUser.tenantId } as Contract;

      mockContractService.findById.mockResolvedValue(mockContract);

      await controller.findOne(contractId, mockUser);

      expect(mockContractService.findById).toHaveBeenCalledWith(Number(mockUser.tenantId), contractId);
      expect(mockContractService.findById).toHaveBeenCalledTimes(1);
    });

    it('should extract tenantId from JWT and pass to service.findByEmployeeId', async () => {
      const employeeId = 1;
      const mockContracts = [{ id: 1, tenantId: mockUser.tenantId, employeeId }] as Contract[];

      mockContractService.findByEmployeeId.mockResolvedValue(mockContracts);

      await controller.findByEmployeeId(employeeId, mockUser);

      expect(mockContractService.findByEmployeeId).toHaveBeenCalledWith(Number(mockUser.tenantId), employeeId);
      expect(mockContractService.findByEmployeeId).toHaveBeenCalledTimes(1);
    });

    it('should extract tenantId from JWT and pass to service.findActiveContractByEmployeeId', async () => {
      const employeeId = 1;
      const mockContract = { id: 1, tenantId: mockUser.tenantId, employeeId } as Contract;

      mockContractService.findActiveContractByEmployeeId.mockResolvedValue(mockContract);

      await controller.findActiveContractByEmployeeId(employeeId, mockUser);

      expect(mockContractService.findActiveContractByEmployeeId).toHaveBeenCalledWith(Number(mockUser.tenantId), employeeId);
      expect(mockContractService.findActiveContractByEmployeeId).toHaveBeenCalledTimes(1);
    });

    it('should extract tenantId from JWT and pass to service.findContractsExpiringSoon', async () => {
      const daysThreshold = 30;
      const mockContracts = [{ id: 1, tenantId: mockUser.tenantId }] as Contract[];

      mockContractService.findContractsExpiringSoon.mockResolvedValue(mockContracts);

      await controller.findContractsExpiringSoon(daysThreshold, mockUser);

      expect(mockContractService.findContractsExpiringSoon).toHaveBeenCalledWith(Number(mockUser.tenantId), daysThreshold);
      expect(mockContractService.findContractsExpiringSoon).toHaveBeenCalledTimes(1);
    });

    it('should extract tenantId from JWT and pass to service.create', async () => {
      const mockCreateDto: CreateContractDto = {
        contractCode: 'CT-2023-001',
        employeeId: 1,
        contractType: ContractType.DEFINITE,
        title: 'Employment Contract',
        startDate: new Date('2023-01-15'),
        baseSalary: 10000000,
      };
      const mockCreatedContract = { id: 1, tenantId: mockUser.tenantId, ...mockCreateDto } as Contract;

      mockContractService.create.mockResolvedValue(mockCreatedContract);

      await controller.create(mockCreateDto, mockUser);

      expect(mockContractService.create).toHaveBeenCalledWith(Number(mockUser.tenantId), mockCreateDto, mockUser.id);
      expect(mockContractService.create).toHaveBeenCalledTimes(1);
    });

    it('should extract tenantId from JWT and pass to service.update', async () => {
      const contractId = 1;
      const mockUpdateDto: UpdateContractDto = { title: 'Updated Contract' };
      const mockUpdatedContract = { id: contractId, tenantId: mockUser.tenantId, ...mockUpdateDto } as Contract;

      mockContractService.update.mockResolvedValue(mockUpdatedContract);

      await controller.update(contractId, mockUpdateDto, mockUser);

      expect(mockContractService.update).toHaveBeenCalledWith(Number(mockUser.tenantId), contractId, mockUpdateDto, mockUser.id);
      expect(mockContractService.update).toHaveBeenCalledTimes(1);
    });

    it('should extract tenantId from JWT and pass to service.delete', async () => {
      const contractId = 1;

      mockContractService.delete.mockResolvedValue(true);

      await controller.remove(contractId, mockUser);

      expect(mockContractService.delete).toHaveBeenCalledWith(Number(mockUser.tenantId), contractId);
      expect(mockContractService.delete).toHaveBeenCalledTimes(1);
    });
  });

  describe('Cross-Tenant Access Prevention at Controller Level', () => {
    const tenant1User: JwtPayload = {
      id: 1,
      email: '<EMAIL>',
      tenantId: 1,
      roles: ['user'],
      iat: Date.now(),
      exp: Date.now() + 3600,
    };

    const tenant2User: JwtPayload = {
      id: 2,
      email: '<EMAIL>',
      tenantId: 2,
      roles: ['user'],
      iat: Date.now(),
      exp: Date.now() + 3600,
    };

    it('should pass different tenantIds for different users', async () => {
      const contractId = 1;
      const mockContract1 = { id: contractId, tenantId: tenant1User.tenantId } as Contract;
      const mockContract2 = { id: contractId, tenantId: tenant2User.tenantId } as Contract;

      mockContractService.findById
        .mockResolvedValueOnce(mockContract1)
        .mockResolvedValueOnce(mockContract2);

      // User from tenant 1 accessing contract
      await controller.findOne(contractId, tenant1User);
      expect(mockContractService.findById).toHaveBeenCalledWith(Number(tenant1User.tenantId), contractId);

      // User from tenant 2 accessing contract
      await controller.findOne(contractId, tenant2User);
      expect(mockContractService.findById).toHaveBeenCalledWith(Number(tenant2User.tenantId), contractId);

      expect(mockContractService.findById).toHaveBeenCalledTimes(2);
    });

    it('should ensure create operations use correct tenantId', async () => {
      const mockCreateDto: CreateContractDto = {
        contractCode: 'CT-2023-001',
        employeeId: 1,
        contractType: ContractType.DEFINITE,
        title: 'Employment Contract',
        startDate: new Date('2023-01-15'),
        baseSalary: 10000000,
      };

      const mockContract1 = { id: 1, tenantId: tenant1User.tenantId, ...mockCreateDto } as Contract;
      const mockContract2 = { id: 2, tenantId: tenant2User.tenantId, ...mockCreateDto } as Contract;

      mockContractService.create
        .mockResolvedValueOnce(mockContract1)
        .mockResolvedValueOnce(mockContract2);

      // User from tenant 1 creating contract
      await controller.create(mockCreateDto, tenant1User);
      expect(mockContractService.create).toHaveBeenCalledWith(Number(tenant1User.tenantId), mockCreateDto, tenant1User.id);

      // User from tenant 2 creating contract
      await controller.create(mockCreateDto, tenant2User);
      expect(mockContractService.create).toHaveBeenCalledWith(Number(tenant2User.tenantId), mockCreateDto, tenant2User.id);

      expect(mockContractService.create).toHaveBeenCalledTimes(2);
    });
  });

  describe('TenantId Type Conversion', () => {
    it('should convert string tenantId to number', async () => {
      const mockUser: JwtPayload = {
        id: 1,
        email: '<EMAIL>',
        tenantId: '1' as any, // Simulating string tenantId from JWT
        roles: ['user'],
        iat: Date.now(),
        exp: Date.now() + 3600,
      };

      const mockQuery = { page: 1, limit: 10 };
      const mockResult = {
        items: [] as Contract[],
        meta: { totalItems: 0, itemCount: 0, itemsPerPage: 10, totalPages: 0, currentPage: 1 },
      };

      mockContractService.findAll.mockResolvedValue(mockResult);

      await controller.findAll(mockQuery, mockUser);

      // Verify that string tenantId is converted to number
      expect(mockContractService.findAll).toHaveBeenCalledWith(1, mockQuery);
      expect(typeof mockContractService.findAll.mock.calls[0][0]).toBe('number');
    });
  });
});
