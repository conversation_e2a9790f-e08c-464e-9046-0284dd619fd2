import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule, getRepositoryToken } from '@nestjs/typeorm';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { Contract } from '../entities/contract.entity';
import { ContractRepository } from '../repositories/contract.repository';
import { ContractStatus } from '../enum/contract-status.enum';
import { ContractType } from '../enum/contract-type.enum';

/**
 * Integration tests for ContractRepository tenant isolation
 * Tests verify that database queries properly include tenantId filtering
 */
describe('ContractRepository Tenant Isolation', () => {
  let module: TestingModule;
  let contractRepository: ContractRepository;
  let nativeRepository: Repository<Contract>;
  let dataSource: DataSource;
  const logger = new Logger('ContractRepositoryTenantTest');

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env',
        }),
        TypeOrmModule.forRootAsync({
          imports: [ConfigModule],
          useFactory: (configService: ConfigService) => ({
            type: 'postgres',
            host: configService.get('DB_HOST'),
            port: configService.get('DB_PORT'),
            username: configService.get('DB_USERNAME'),
            password: configService.get('DB_PASSWORD'),
            database: configService.get('DB_DATABASE'),
            entities: [Contract],
            synchronize: false,
            logging: false,
          }),
          inject: [ConfigService],
        }),
        TypeOrmModule.forFeature([Contract]),
      ],
      providers: [ContractRepository],
    }).compile();

    contractRepository = module.get<ContractRepository>(ContractRepository);
    nativeRepository = module.get<Repository<Contract>>(getRepositoryToken(Contract));
    dataSource = module.get<DataSource>(DataSource);
  });

  afterAll(async () => {
    await module.close();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Query Builder Tenant Filtering', () => {
    const testTenantId = 1;

    it('should add tenantId condition to findAll query', async () => {
      // Mock the query builder to capture the where conditions
      const mockQueryBuilder = {
        andWhere: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
      } as unknown as SelectQueryBuilder<Contract>;

      const createQueryBuilderSpy = jest.spyOn(nativeRepository, 'createQueryBuilder')
        .mockReturnValue(mockQueryBuilder);

      await contractRepository.findAll(testTenantId, { page: 1, limit: 10 });

      expect(createQueryBuilderSpy).toHaveBeenCalledWith('contract');
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'contract.tenantId = :tenantId',
        { tenantId: testTenantId }
      );
    });

    it('should include tenantId in findById where clause', async () => {
      const contractId = 1;
      const findOneSpy = jest.spyOn(nativeRepository, 'findOne')
        .mockResolvedValue(null);

      await contractRepository.findById(testTenantId, contractId);

      expect(findOneSpy).toHaveBeenCalledWith({
        where: { id: contractId, tenantId: testTenantId },
      });
    });

    it('should include tenantId in findByContractCode where clause', async () => {
      const contractCode = 'CT-2023-001';
      const findOneSpy = jest.spyOn(nativeRepository, 'findOne')
        .mockResolvedValue(null);

      await contractRepository.findByContractCode(testTenantId, contractCode);

      expect(findOneSpy).toHaveBeenCalledWith({
        where: { contractCode, tenantId: testTenantId },
      });
    });

    it('should include tenantId in findByEmployeeId where clause', async () => {
      const employeeId = 1;
      const findSpy = jest.spyOn(nativeRepository, 'find')
        .mockResolvedValue([]);

      await contractRepository.findByEmployeeId(testTenantId, employeeId);

      expect(findSpy).toHaveBeenCalledWith({
        where: { employeeId, tenantId: testTenantId },
        order: { startDate: 'DESC' },
      });
    });

    it('should include tenantId in findActiveContractByEmployeeId where clause', async () => {
      const employeeId = 1;
      const findOneSpy = jest.spyOn(nativeRepository, 'findOne')
        .mockResolvedValue(null);

      await contractRepository.findActiveContractByEmployeeId(testTenantId, employeeId);

      expect(findOneSpy).toHaveBeenCalledWith({
        where: expect.objectContaining({
          employeeId,
          tenantId: testTenantId,
          status: ContractStatus.ACTIVE,
        }),
      });
    });

    it('should include tenantId in findContractsExpiringSoon where clause', async () => {
      const daysThreshold = 30;
      const findSpy = jest.spyOn(nativeRepository, 'find')
        .mockResolvedValue([]);

      await contractRepository.findContractsExpiringSoon(testTenantId, daysThreshold);

      expect(findSpy).toHaveBeenCalledWith({
        where: expect.objectContaining({
          tenantId: testTenantId,
          status: ContractStatus.ACTIVE,
        }),
        order: { endDate: 'ASC' },
      });
    });
  });

  describe('Create and Update Operations', () => {
    const testTenantId = 1;

    it('should set tenantId when creating contract', async () => {
      const contractData = {
        contractCode: 'CT-TEST-001',
        employeeId: 1,
        contractType: ContractType.DEFINITE,
        title: 'Test Contract',
        startDate: new Date(),
        baseSalary: 10000000,
      };

      const createSpy = jest.spyOn(nativeRepository, 'create')
        .mockReturnValue(contractData as Contract);
      const saveSpy = jest.spyOn(nativeRepository, 'save')
        .mockResolvedValue({ ...contractData, id: 1, tenantId: testTenantId } as Contract);

      await contractRepository.create(testTenantId, contractData);

      expect(createSpy).toHaveBeenCalledWith({ ...contractData, tenantId: testTenantId });
      expect(saveSpy).toHaveBeenCalled();
    });

    it('should include tenantId in update where clause', async () => {
      const contractId = 1;
      const updateData = { title: 'Updated Contract' };
      
      const updateSpy = jest.spyOn(nativeRepository, 'update')
        .mockResolvedValue({ affected: 1, generatedMaps: [], raw: [] });
      
      const findByIdSpy = jest.spyOn(contractRepository, 'findById')
        .mockResolvedValue({ id: contractId, tenantId: testTenantId } as Contract);

      await contractRepository.update(testTenantId, contractId, updateData);

      expect(updateSpy).toHaveBeenCalledWith(
        { id: contractId, tenantId: testTenantId },
        updateData
      );
    });

    it('should include tenantId in delete where clause', async () => {
      const contractId = 1;
      
      const deleteSpy = jest.spyOn(nativeRepository, 'delete')
        .mockResolvedValue({ affected: 1, raw: [] });

      await contractRepository.delete(testTenantId, contractId);

      expect(deleteSpy).toHaveBeenCalledWith({ id: contractId, tenantId: testTenantId });
    });
  });

  describe('Cross-Tenant Data Isolation', () => {
    const tenant1Id = 1;
    const tenant2Id = 2;
    const contractId = 1;

    it('should not find contract from different tenant', async () => {
      const findOneSpy = jest.spyOn(nativeRepository, 'findOne')
        .mockResolvedValue(null); // Simulating no contract found for different tenant

      const result = await contractRepository.findById(tenant1Id, contractId);

      expect(result).toBeNull();
      expect(findOneSpy).toHaveBeenCalledWith({
        where: { id: contractId, tenantId: tenant1Id },
      });
    });

    it('should not update contract from different tenant', async () => {
      const updateData = { title: 'Updated Contract' };
      
      const updateSpy = jest.spyOn(nativeRepository, 'update')
        .mockResolvedValue({ affected: 0, generatedMaps: [], raw: [] }); // No rows affected
      
      const findByIdSpy = jest.spyOn(contractRepository, 'findById')
        .mockResolvedValue(null); // Contract not found for this tenant

      const result = await contractRepository.update(tenant1Id, contractId, updateData);

      expect(result).toBeNull();
      expect(updateSpy).toHaveBeenCalledWith(
        { id: contractId, tenantId: tenant1Id },
        updateData
      );
    });

    it('should not delete contract from different tenant', async () => {
      const deleteSpy = jest.spyOn(nativeRepository, 'delete')
        .mockResolvedValue({ affected: 0, raw: [] }); // No rows affected

      const result = await contractRepository.delete(tenant1Id, contractId);

      expect(result).toBe(false);
      expect(deleteSpy).toHaveBeenCalledWith({ id: contractId, tenantId: tenant1Id });
    });
  });

  describe('Tenant Data Consistency Validation', () => {
    const testTenantId = 1;

    it('should ensure all query results belong to correct tenant', async () => {
      const mockContracts = [
        { id: 1, tenantId: testTenantId, contractCode: 'CT-001' },
        { id: 2, tenantId: testTenantId, contractCode: 'CT-002' },
      ] as Contract[];

      const mockQueryBuilder = {
        andWhere: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([mockContracts, mockContracts.length]),
      } as unknown as SelectQueryBuilder<Contract>;

      jest.spyOn(nativeRepository, 'createQueryBuilder').mockReturnValue(mockQueryBuilder);

      const result = await contractRepository.findAll(testTenantId, { page: 1, limit: 10 });

      // Verify all returned contracts have the correct tenantId
      result.items.forEach(contract => {
        expect(contract.tenantId).toBe(testTenantId);
      });
    });
  });
});
